// Simple test script to generate a ByteDance video
const fetch = require('node-fetch');

async function testBytedanceVideo() {
  try {
    const response = await fetch('http://localhost:3001/api/generate/video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // This will fail but we can see the logs
      },
      body: JSON.stringify({
        prompt: 'cat playing with ball',
        duration: 5,
        aspectRatio: '16:9',
        style: 'realistic',
        quality: 'standard',
        resolution: '720p',
        provider: 'bytedance',
        sourceType: 'text-to-video'
      })
    });

    const result = await response.text();
    console.log('Response:', result);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testBytedanceVideo();
