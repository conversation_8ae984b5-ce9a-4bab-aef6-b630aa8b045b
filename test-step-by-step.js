const http = require('http');

async function testVideoGenerationStepByStep() {
  console.log('🎬 VIDEO GENERATION PROCESS - STEP BY STEP VERIFICATION');
  console.log('======================================================');
  console.log('');
  
  let generationId = null;
  let gcsOutputDirectory = null;
  let operationName = null;
  
  // STEP 1: Test Video Generation Initiation
  console.log('📋 STEP 1: Video Generation Initiation');
  console.log('--------------------------------------');
  
  const step1Result = await testStep1_VideoGeneration();
  if (!step1Result.success) {
    console.log('❌ STEP 1 FAILED - Cannot proceed');
    return false;
  }
  
  generationId = step1Result.generationId;
  gcsOutputDirectory = step1Result.gcsOutputDirectory;
  operationName = step1Result.operationName;
  
  console.log('✅ STEP 1 PASSED');
  console.log('');
  
  // STEP 2: Test Google Veo API Integration
  console.log('📋 STEP 2: Google Veo API Integration');
  console.log('------------------------------------');
  
  const step2Result = await testStep2_GoogleVeoAPI(operationName);
  if (!step2Result.success) {
    console.log('❌ STEP 2 FAILED - Google Veo API issues');
    return false;
  }
  
  console.log('✅ STEP 2 PASSED');
  console.log('');
  
  // STEP 3: Test GCS Bucket Configuration
  console.log('📋 STEP 3: GCS Bucket Configuration');
  console.log('----------------------------------');
  
  const step3Result = await testStep3_GCSBucket(gcsOutputDirectory);
  if (!step3Result.success) {
    console.log('❌ STEP 3 FAILED - GCS Bucket issues');
    return false;
  }
  
  console.log('✅ STEP 3 PASSED');
  console.log('');
  
  // STEP 4: Test Polling Mechanism
  console.log('📋 STEP 4: Polling Mechanism');
  console.log('----------------------------');
  
  const step4Result = await testStep4_PollingMechanism(generationId, gcsOutputDirectory);
  if (!step4Result.success) {
    console.log('❌ STEP 4 FAILED - Polling mechanism issues');
    return false;
  }
  
  console.log('✅ STEP 4 PASSED');
  console.log('');
  
  // STEP 5: Test Database Integration
  console.log('📋 STEP 5: Database Integration');
  console.log('------------------------------');
  
  const step5Result = await testStep5_DatabaseIntegration();
  if (!step5Result.success) {
    console.log('❌ STEP 5 FAILED - Database issues');
    return false;
  }
  
  console.log('✅ STEP 5 PASSED');
  console.log('');
  
  // STEP 6: Test End-to-End Flow
  console.log('📋 STEP 6: End-to-End Flow Verification');
  console.log('---------------------------------------');
  
  const step6Result = await testStep6_EndToEndFlow();
  if (!step6Result.success) {
    console.log('❌ STEP 6 FAILED - End-to-end flow issues');
    return false;
  }
  
  console.log('✅ STEP 6 PASSED');
  console.log('');
  
  console.log('🎉 ALL STEPS PASSED! VIDEO GENERATION PROCESS IS FULLY OPERATIONAL!');
  return true;
}

async function testStep1_VideoGeneration() {
  console.log('  🚀 Testing video generation API endpoint...');
  
  const postData = JSON.stringify({
    prompt: 'STEP-BY-STEP TEST: A beautiful sunset over mountains',
    duration: 5,
    aspectRatio: '16:9',
    resolution: '720p',
    provider: 'google-veo',
    testMode: true
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/generate/video',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  return new Promise((resolve) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          
          console.log('  📊 Response Status:', res.statusCode);
          console.log('  📊 Success:', result.success);
          console.log('  📊 Status:', result.status);
          console.log('  📊 Generation ID:', result.generationId);
          console.log('  📊 Operation Name:', result.operationName ? 'Present' : 'Missing');
          console.log('  📊 GCS Directory:', result.gcsOutputDirectory);
          
          if (res.statusCode === 200 && result.success && result.generationId && result.operationName) {
            resolve({
              success: true,
              generationId: result.generationId,
              gcsOutputDirectory: result.gcsOutputDirectory,
              operationName: result.operationName
            });
          } else {
            resolve({ success: false, error: result.error || 'Invalid response' });
          }
        } catch (e) {
          console.log('  ❌ Parse error:', e.message);
          resolve({ success: false, error: 'Parse error' });
        }
      });
    });

    req.on('error', (error) => {
      console.log('  ❌ Request error:', error.message);
      resolve({ success: false, error: error.message });
    });

    req.write(postData);
    req.end();
  });
}

async function testStep2_GoogleVeoAPI(operationName) {
  console.log('  🤖 Verifying Google Veo API integration...');
  
  if (!operationName) {
    console.log('  ❌ No operation name provided');
    return { success: false };
  }
  
  // Check if operation name has correct format
  const expectedPattern = /^projects\/gensy-463405\/locations\/us-central1\/publishers\/google\/models\/veo-2\.0-generate-001\/operations\/[a-f0-9-]+$/;
  
  if (expectedPattern.test(operationName)) {
    console.log('  ✅ Operation name format is correct');
    console.log('  ✅ Google Veo API integration working');
    return { success: true };
  } else {
    console.log('  ❌ Operation name format is incorrect:', operationName);
    return { success: false };
  }
}

async function testStep3_GCSBucket(gcsOutputDirectory) {
  console.log('  🗂️ Verifying GCS bucket configuration...');
  
  if (!gcsOutputDirectory) {
    console.log('  ❌ No GCS output directory provided');
    return { success: false };
  }
  
  console.log('  📍 GCS Directory:', gcsOutputDirectory);
  
  // Check if using correct bucket
  if (gcsOutputDirectory.includes('gensy-final')) {
    console.log('  ✅ Using correct bucket: gensy-final');
    
    // Check directory structure
    if (gcsOutputDirectory.includes('/video-outputs/')) {
      console.log('  ✅ Directory structure is correct');
      return { success: true };
    } else {
      console.log('  ❌ Directory structure is incorrect');
      return { success: false };
    }
  } else {
    console.log('  ❌ Using incorrect bucket');
    return { success: false };
  }
}

async function testStep4_PollingMechanism(generationId, gcsOutputDirectory) {
  console.log('  🔄 Testing polling mechanism...');
  
  const pollData = JSON.stringify({
    generationId: generationId,
    gcsOutputDirectory: gcsOutputDirectory,
    testMode: true
  });

  const pollOptions = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/generate/video/poll',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(pollData)
    }
  };

  return new Promise((resolve) => {
    const req = http.request(pollOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          
          console.log('  📊 Polling Status:', res.statusCode);
          console.log('  📊 Polling Success:', result.success);
          console.log('  📊 Video Status:', result.status);
          
          if (res.statusCode === 200 && result.success !== false) {
            console.log('  ✅ Polling mechanism working correctly');
            resolve({ success: true });
          } else {
            console.log('  ❌ Polling mechanism failed:', result.error);
            resolve({ success: false, error: result.error });
          }
        } catch (e) {
          console.log('  ❌ Polling parse error:', e.message);
          resolve({ success: false, error: 'Parse error' });
        }
      });
    });

    req.on('error', (error) => {
      console.log('  ❌ Polling request error:', error.message);
      resolve({ success: false, error: error.message });
    });

    req.write(pollData);
    req.end();
  });
}

async function testStep5_DatabaseIntegration() {
  console.log('  🗄️ Testing database integration...');
  
  // Test generations API
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/generations?type=video&limit=5',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          
          console.log('  📊 Database API Status:', res.statusCode);
          console.log('  📊 Database Response:', result.success !== false ? 'Success' : 'Failed');
          
          if (res.statusCode === 200) {
            console.log('  ✅ Database integration working');
            resolve({ success: true });
          } else {
            console.log('  ❌ Database integration failed');
            resolve({ success: false });
          }
        } catch (e) {
          console.log('  ❌ Database parse error:', e.message);
          resolve({ success: false });
        }
      });
    });

    req.on('error', (error) => {
      console.log('  ❌ Database request error:', error.message);
      resolve({ success: false });
    });

    req.end();
  });
}

async function testStep6_EndToEndFlow() {
  console.log('  🔄 Testing complete end-to-end flow...');
  
  // Generate a new video and immediately poll
  const generationResult = await testStep1_VideoGeneration();
  if (!generationResult.success) {
    return { success: false };
  }
  
  // Wait a moment and poll
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const pollingResult = await testStep4_PollingMechanism(
    generationResult.generationId, 
    generationResult.gcsOutputDirectory
  );
  
  if (pollingResult.success) {
    console.log('  ✅ End-to-end flow working correctly');
    return { success: true };
  } else {
    console.log('  ❌ End-to-end flow failed');
    return { success: false };
  }
}

// Run the step-by-step test
testVideoGenerationStepByStep().then(success => {
  console.log('');
  console.log('🎯 STEP-BY-STEP VERIFICATION COMPLETE');
  console.log('====================================');
  if (success) {
    console.log('🎊 RESULT: ALL SYSTEMS OPERATIONAL!');
    console.log('✅ Video generation process is ready for production');
  } else {
    console.log('💥 RESULT: ISSUES DETECTED');
    console.log('❌ Manual intervention required');
  }
  process.exit(success ? 0 : 1);
});
