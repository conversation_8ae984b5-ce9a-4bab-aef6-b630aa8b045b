/**
 * Test script to verify the bucket configuration is correct
 */

require('dotenv').config({ path: '.env.local' });

async function testBucketConfig() {
  console.log('🧪 Testing Google Cloud Storage Bucket Configuration...\n');
  
  // Test environment variables
  console.log('📋 Environment Variables:');
  console.log(`   GOOGLE_CLOUD_PROJECT_ID: ${process.env.GOOGLE_CLOUD_PROJECT_ID}`);
  console.log(`   GOOGLE_CLOUD_STORAGE_BUCKET: ${process.env.GOOGLE_CLOUD_STORAGE_BUCKET}`);
  console.log(`   GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
  console.log('');
  
  // Test bucket name in GCS output directory format
  const bucketName = process.env.GOOGLE_CLOUD_STORAGE_BUCKET || 'gensy-final';
  const testGenerationId = 'test-generation-123';
  const gcsOutputDirectory = `gs://${bucketName}/video-outputs/${testGenerationId}`;
  
  console.log('🗂️ GCS Output Directory Test:');
  console.log(`   Generated GCS URI: ${gcsOutputDirectory}`);
  console.log('');
  
  // Parse the GCS URI like the polling route does
  const gcsUri = gcsOutputDirectory.replace('gs://', '');
  const [extractedBucketName, ...prefixParts] = gcsUri.split('/');
  const prefix = prefixParts.join('/');
  
  console.log('🔍 Parsed GCS URI:');
  console.log(`   Bucket Name: ${extractedBucketName}`);
  console.log(`   Prefix: ${prefix}`);
  console.log('');
  
  // Verify bucket name matches expected
  if (extractedBucketName === 'gensy-final') {
    console.log('✅ SUCCESS: Bucket name is correct (gensy-final)');
  } else {
    console.log(`❌ ERROR: Expected 'gensy-final', got '${extractedBucketName}'`);
  }
  
  // Test credentials file exists
  const fs = require('fs');
  const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
  
  if (fs.existsSync(credentialsPath)) {
    console.log('✅ SUCCESS: Credentials file exists');
  } else {
    console.log('❌ ERROR: Credentials file not found');
  }
  
  console.log('\n🎉 Bucket configuration test completed!');
}

testBucketConfig().catch(console.error);
