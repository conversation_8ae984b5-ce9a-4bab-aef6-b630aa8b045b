{"sprint": {"id": 6, "name": "AI Creative Suite - Payments & Monetization", "duration": "2 weeks", "goal": "Implement PhonePe payment integration, subscription system, and comprehensive monetization features", "priority": "HIGH", "startDate": "2025-03-24", "endDate": "2025-04-06"}, "tasks": [{"id": "ACS-037", "title": "Set Up PhonePe Payment Integration", "description": "Configure PhonePe payment gateway for subscription and credit purchases", "details": "Set up PhonePe integration for secure payment processing.\n\n1. **PhonePe Service Implementation:**\n```typescript\n// src/lib/services/phonepe.ts\nexport class PhonePeService {\n  static async createPaymentRequest(\n    amount: number,\n    userId: string,\n    planId?: string\n  ) {\n    try {\n      const merchantTransactionId = `TXN_${Date.now()}_${userId}`\n      \n      const paymentData = {\n        merchantId: process.env.PHONEPE_MERCHANT_ID!,\n        merchantTransactionId,\n        merchantUserId: userId,\n        amount: amount * 100, // Convert to paise\n        redirectUrl: `${process.env.NEXT_PUBLIC_APP_URL}/payment/callback`,\n        redirectMode: 'POST',\n        callbackUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/phonepe`,\n        paymentInstrument: {\n          type: 'PAY_PAGE'\n        }\n      }\n      \n      const base64Data = Buffer.from(JSON.stringify(paymentData)).toString('base64')\n      const checksum = this.generateChecksum(base64Data)\n      \n      const response = await fetch('https://api.phonepe.com/apis/hermes/pg/v1/pay', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-VERIFY': checksum\n        },\n        body: JSON.stringify({\n          request: base64Data\n        })\n      })\n      \n      return await response.json()\n    } catch (error) {\n      throw new Error(`PhonePe payment creation failed: ${error.message}`)\n    }\n  }\n  \n  static generateChecksum(data: string): string {\n    const crypto = require('crypto')\n    const saltKey = process.env.PHONEPE_SALT_KEY!\n    const saltIndex = process.env.PHONEPE_SALT_INDEX!\n    \n    const string = data + '/pg/v1/pay' + saltKey\n    const sha256 = crypto.createHash('sha256').update(string).digest('hex')\n    return sha256 + '###' + saltIndex\n  }\n}\n```\n\n**MCP Usage:** Use Context 7 MCP to research PhonePe API documentation and payment security best practices.", "testStrategy": "1. Test payment request creation\n2. Test checksum generation\n3. Test payment flow end-to-end\n4. Test webhook handling\n5. Test error scenarios", "priority": "high", "dependencies": ["ACS-002"], "status": "pending", "complexity": "High", "subtasks": []}, {"id": "ACS-038", "title": "Create Subscription Plans System", "description": "Design and implement subscription plans with different feature tiers", "details": "Create a comprehensive subscription system with multiple tiers and features.\n\n1. **Subscription Plans Schema:**\n```sql\n-- Subscription plans table\nCREATE TABLE subscription_plans (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  name TEXT NOT NULL,\n  description TEXT,\n  price_monthly DECIMAL(10,2),\n  price_yearly DECIMAL(10,2),\n  credits_per_month INTEGER,\n  features JSONB,\n  is_active BOOLEAN DEFAULT true,\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n\n-- User subscriptions table\nCREATE TABLE user_subscriptions (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  user_id UUID REFERENCES users(id) ON DELETE CASCADE,\n  plan_id UUID REFERENCES subscription_plans(id),\n  status TEXT CHECK (status IN ('active', 'cancelled', 'expired', 'pending')),\n  current_period_start TIMESTAMP WITH TIME ZONE,\n  current_period_end TIMESTAMP WITH TIME ZONE,\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n);\n```\n\n2. **Subscription Plans:**\n- **Free Plan:** 10 credits/month, basic features\n- **Pro Plan:** $9.99/month, 100 credits/month, advanced features\n- **Premium Plan:** $19.99/month, 500 credits/month, all features\n- **Enterprise Plan:** $49.99/month, unlimited credits, priority support\n\n3. **Subscription Service:**\n```typescript\n// src/lib/services/subscription.ts\nexport class SubscriptionService {\n  static async createSubscription(\n    userId: string,\n    planId: string,\n    paymentId: string\n  ) {\n    const plan = await this.getPlan(planId)\n    const startDate = new Date()\n    const endDate = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000) // 30 days\n    \n    return await supabase\n      .from('user_subscriptions')\n      .insert({\n        user_id: userId,\n        plan_id: planId,\n        status: 'active',\n        current_period_start: startDate.toISOString(),\n        current_period_end: endDate.toISOString()\n      })\n      .select()\n      .single()\n  }\n}\n```\n\n**MCP Usage:** Use Supabase MCP to create subscription tables and implement RLS policies.", "testStrategy": "1. Test subscription plan creation\n2. Test subscription activation\n3. Test plan upgrades/downgrades\n4. Test subscription cancellation\n5. Test billing cycle management", "priority": "high", "dependencies": ["ACS-004"], "status": "pending", "complexity": "High", "subtasks": []}, {"id": "ACS-039", "title": "Create Payment API Endpoints", "description": "Build API endpoints for payment initiation, webhooks, and subscription management", "details": "Create comprehensive API endpoints for payment processing and subscription management.\n\n**Payment API Endpoints:**\n1. **Payment Initiation:**\n```typescript\n// src/app/api/payments/initiate/route.ts\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId } = auth()\n    if (!userId) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { planId, amount, type } = await request.json()\n    \n    // Create payment request\n    const paymentResult = await PhonePeService.createPaymentRequest(\n      amount,\n      userId,\n      planId\n    )\n    \n    // Store payment record\n    await supabase\n      .from('payments')\n      .insert({\n        user_id: userId,\n        transaction_id: paymentResult.merchantTransactionId,\n        amount,\n        type, // 'subscription' or 'credits'\n        plan_id: planId,\n        status: 'pending'\n      })\n    \n    return NextResponse.json({\n      success: true,\n      paymentUrl: paymentResult.data.instrumentResponse.redirectInfo.url\n    })\n  } catch (error) {\n    return NextResponse.json({ error: 'Payment initiation failed' }, { status: 500 })\n  }\n}\n```\n\n2. **PhonePe Webhook:**\n```typescript\n// src/app/api/webhooks/phonepe/route.ts\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.text()\n    const signature = request.headers.get('X-VERIFY')\n    \n    // Verify webhook signature\n    if (!PhonePeService.verifyWebhookSignature(body, signature)) {\n      return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })\n    }\n    \n    const webhookData = JSON.parse(body)\n    const { transactionId, status, amount } = webhookData\n    \n    if (status === 'PAYMENT_SUCCESS') {\n      await this.handleSuccessfulPayment(transactionId, amount)\n    } else {\n      await this.handleFailedPayment(transactionId)\n    }\n    \n    return NextResponse.json({ success: true })\n  } catch (error) {\n    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })\n  }\n}\n```\n\n**MCP Usage:** Use Sequential thinking MCP to design secure webhook processing and payment validation workflows.", "testStrategy": "1. Test payment initiation API\n2. Test webhook signature verification\n3. Test successful payment processing\n4. Test failed payment handling\n5. Test subscription activation", "priority": "high", "dependencies": ["ACS-037", "ACS-038"], "status": "pending", "complexity": "High", "subtasks": []}, {"id": "ACS-040", "title": "Create Pricing Page UI", "description": "Design and implement pricing page with subscription plans and features comparison", "details": "Create an attractive and informative pricing page that showcases subscription plans.\n\n**Pricing Page Components:**\n```typescript\n// src/components/pricing/PricingPage.tsx\nexport function PricingPage() {\n  const [billingCycle, setBillingCycle] = useState('monthly')\n  const [plans, setPlans] = useState([])\n  \n  return (\n    <div className=\"pricing-page\">\n      <PricingHeader />\n      \n      <BillingToggle\n        value={billingCycle}\n        onChange={setBillingCycle}\n        options={['monthly', 'yearly']}\n      />\n      \n      <PricingGrid\n        plans={plans}\n        billingCycle={billingCycle}\n        onSelectPlan={handlePlanSelection}\n      />\n      \n      <FeaturesComparison plans={plans} />\n      \n      <PricingFAQ />\n    </div>\n  )\n}\n```\n\n**Features to highlight:**\n- Credit allocations per plan\n- Feature access levels\n- Priority support\n- API access\n- Commercial usage rights\n- Advanced generation options\n\n**UI Elements:**\n- Plan comparison table\n- Feature checkmarks\n- Popular plan highlighting\n- Annual discount badges\n- Call-to-action buttons\n- Money-back guarantee\n\n**MCP Usage:** Use Context 7 MCP to research pricing page design patterns and conversion optimization techniques.", "testStrategy": "1. Test pricing plan display\n2. Test billing cycle toggle\n3. Test plan selection flow\n4. Test responsive design\n5. Test conversion tracking", "priority": "medium", "dependencies": ["ACS-038"], "status": "pending", "complexity": "Medium", "subtasks": []}, {"id": "ACS-041", "title": "Implement Credit Purchase System", "description": "Create system for purchasing individual credit packages", "details": "Implement a flexible credit purchase system for users who prefer pay-as-you-go.\n\n**Credit Packages:**\n- **Starter Pack:** $4.99 for 25 credits\n- **Value Pack:** $9.99 for 60 credits (20% bonus)\n- **Power Pack:** $19.99 for 150 credits (50% bonus)\n- **Mega Pack:** $39.99 for 350 credits (75% bonus)\n\n**Credit Purchase Service:**\n```typescript\n// src/lib/services/credit-purchase.ts\nexport class CreditPurchaseService {\n  static async purchaseCredits(\n    userId: string,\n    packageId: string,\n    paymentId: string\n  ) {\n    const package = await this.getCreditPackage(packageId)\n    \n    // Add credits to user account\n    const { data } = await supabase\n      .from('users')\n      .update({\n        credits: supabase.raw('credits + ?', [package.credits])\n      })\n      .eq('clerk_user_id', userId)\n      .select('credits')\n      .single()\n    \n    // Log credit transaction\n    await supabase\n      .from('credit_transactions')\n      .insert({\n        user_id: userId,\n        type: 'purchase',\n        amount: package.credits,\n        payment_id: paymentId,\n        description: `Purchased ${package.name}`\n      })\n    \n    return data.credits\n  }\n}\n```\n\n**Credit Transaction Tracking:**\n- Purchase history\n- Usage tracking\n- Balance notifications\n- Expiration management\n\n**MCP Usage:** Use Supabase MCP to implement credit transaction logging and balance management.", "testStrategy": "1. Test credit package purchase\n2. Test credit balance updates\n3. Test transaction logging\n4. Test credit expiration\n5. Test purchase history", "priority": "medium", "dependencies": ["ACS-039"], "status": "pending", "complexity": "Medium", "subtasks": []}, {"id": "ACS-042", "title": "Create Billing Dashboard", "description": "Build comprehensive billing dashboard for subscription and payment management", "details": "Create a user-friendly billing dashboard for managing subscriptions and viewing payment history.\n\n**Dashboard Components:**\n```typescript\n// src/components/billing/BillingDashboard.tsx\nexport function BillingDashboard() {\n  const [subscription, setSubscription] = useState(null)\n  const [paymentHistory, setPaymentHistory] = useState([])\n  const [creditBalance, setCreditBalance] = useState(0)\n  \n  return (\n    <div className=\"billing-dashboard\">\n      <BillingHeader />\n      \n      <div className=\"billing-grid\">\n        <CurrentPlanCard\n          subscription={subscription}\n          onUpgrade={handleUpgrade}\n          onCancel={handleCancel}\n        />\n        \n        <CreditBalanceCard\n          balance={creditBalance}\n          onPurchase={handleCreditPurchase}\n        />\n        \n        <UsageStatsCard />\n        \n        <PaymentMethodCard />\n      </div>\n      \n      <PaymentHistoryTable\n        payments={paymentHistory}\n        onDownloadInvoice={handleInvoiceDownload}\n      />\n      \n      <BillingSettings />\n    </div>\n  )\n}\n```\n\n**Features:**\n- Current subscription status\n- Credit balance and usage\n- Payment history\n- Invoice downloads\n- Payment method management\n- Subscription upgrades/downgrades\n- Cancellation options\n- Usage analytics\n\n**MCP Usage:** Use Context 7 MCP to research billing dashboard UI patterns and user experience best practices.", "testStrategy": "1. Test dashboard data loading\n2. Test subscription management\n3. Test payment history display\n4. Test invoice generation\n5. Test responsive design", "priority": "medium", "dependencies": ["ACS-041"], "status": "pending", "complexity": "High", "subtasks": []}, {"id": "ACS-043", "title": "Implement Usage Analytics and Reporting", "description": "Create analytics system for tracking user usage and generating reports", "details": "Implement comprehensive usage analytics for both users and administrators.\n\n**Analytics Features:**\n1. **User Analytics:**\n```typescript\n// src/lib/services/analytics.ts\nexport class AnalyticsService {\n  static async trackGeneration(\n    userId: string,\n    type: 'image' | 'video' | 'upscale',\n    creditsUsed: number,\n    metadata: any\n  ) {\n    await supabase\n      .from('usage_analytics')\n      .insert({\n        user_id: userId,\n        event_type: 'generation',\n        generation_type: type,\n        credits_used: creditsUsed,\n        metadata,\n        timestamp: new Date().toISOString()\n      })\n  }\n  \n  static async getUserUsageStats(userId: string, period: string) {\n    const { data } = await supabase\n      .from('usage_analytics')\n      .select('*')\n      .eq('user_id', userId)\n      .gte('timestamp', this.getPeriodStart(period))\n    \n    return this.aggregateUsageData(data)\n  }\n}\n```\n\n2. **Usage Metrics:**\n- Daily/weekly/monthly generation counts\n- Credit consumption patterns\n- Feature usage breakdown\n- Peak usage times\n- Model preference analysis\n\n3. **Reporting Dashboard:**\n- Usage charts and graphs\n- Cost analysis\n- Efficiency metrics\n- Trend analysis\n- Export capabilities\n\n**MCP Usage:** Use Sequential thinking MCP to design efficient analytics data collection and aggregation strategies.", "testStrategy": "1. Test usage tracking accuracy\n2. Test analytics data aggregation\n3. Test report generation\n4. Test dashboard visualization\n5. Test data export functionality", "priority": "low", "dependencies": ["ACS-042"], "status": "pending", "complexity": "Medium", "subtasks": []}], "successCriteria": ["PhonePe payment integration working securely", "Subscription plans system managing user tiers", "Payment API endpoints handling all transactions", "Pricing page converting visitors to customers", "Credit purchase system providing flexible options", "Billing dashboard managing user accounts", "Usage analytics tracking user behavior", "Webhook processing ensuring payment reliability"]}