/**
 * Manual Polling Endpoint Test
 * This script tests our polling endpoint with real operation data
 */

async function testPollingEndpoint() {
  try {
    console.log('🔄 Testing Polling Endpoint...')
    
    // Use the operation data from our recent test
    const testData = {
      generationId: 'a865a9a1-1102-43ca-be0f-22e6eff11e4f',
      operationName: 'projects/gensy-463405/locations/us-central1/publishers/google/models/veo-2.0-generate-001/operations/82184331-1096-4f1a-9e91-8b3152a77c09'
    }
    
    console.log('📋 Test Data:')
    console.log('- Generation ID:', testData.generationId)
    console.log('- Operation Name:', testData.operationName)
    
    // Make request to our polling endpoint
    const response = await fetch('http://localhost:3002/api/generate/video/poll', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    })
    
    console.log(`📡 Polling Response Status: ${response.status}`)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Polling Response Data:')
      console.log(JSON.stringify(data, null, 2))
      
      // Analyze the response
      if (data.status === 'completed') {
        console.log('🎉 Video generation completed!')
        if (data.videoUrl) {
          console.log('📹 Video URL:', data.videoUrl)
        }
      } else if (data.status === 'failed') {
        console.log('❌ Video generation failed')
        console.log('🚨 Error:', data.error)
      } else if (data.status === 'processing') {
        console.log('⏳ Video generation still in progress')
        console.log('📊 Progress:', data.progress || 'Unknown')
      }
    } else {
      const errorText = await response.text()
      console.error(`❌ Polling Error: ${response.status} - ${errorText}`)
    }
    
  } catch (error) {
    console.error('❌ Error testing polling endpoint:', error)
  }
}

// Run the test
testPollingEndpoint()
