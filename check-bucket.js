/**
 * Check if the GCS bucket exists and list its contents
 */

const { Storage } = require('@google-cloud/storage');

async function checkBucket() {
  try {
    console.log('🗂️ Checking GCS bucket for video outputs...');
    
    // Initialize Google Cloud Storage with service account
    const storage = new Storage({
      projectId: 'gensy-final-464206',
      keyFilename: './sprint/gensy-final-464206-1327695f628d.json'
    });

    const bucketName = 'gensy-final';
    
    // Check if bucket exists
    const [exists] = await storage.bucket(bucketName).exists();
    
    if (exists) {
      console.log(`✅ Bucket ${bucketName} exists!`);
      
      // List contents
      const [files] = await storage.bucket(bucketName).getFiles();
      console.log(`📁 Bucket contains ${files.length} files:`);
      
      files.forEach(file => {
        console.log(`  - ${file.name}`);
      });
      
    } else {
      console.log(`❌ Bucket ${bucketName} does not exist.`);
      console.log('🔧 You need to create this bucket manually in Google Cloud Console.');
      console.log('📍 Suggested settings:');
      console.log('   - Name: gensy-final');
      console.log('   - Location: us-central1');
      console.log('   - Storage class: Standard');
    }
    
  } catch (error) {
    console.error('❌ Error checking bucket:', error.message);
  }
}

checkBucket();
