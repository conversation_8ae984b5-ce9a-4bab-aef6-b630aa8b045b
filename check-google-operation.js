/**
 * Manual Google Vertex AI Operation Status Checker
 * This script directly queries Google Vertex AI to check operation status
 */

const { GoogleAuth } = require('google-auth-library')

async function checkOperationStatus() {
  try {
    console.log('🔍 Checking Google Vertex AI Operation Status...')
    
    // Initialize Google Auth
    const auth = new GoogleAuth({
      keyFile: 'sprint/gensy-463405-296a4152708b.json',
      scopes: ['https://www.googleapis.com/auth/cloud-platform']
    })
    
    const authClient = await auth.getClient()
    const accessToken = await authClient.getAccessToken()
    
    console.log('✅ Authentication successful')
    
    // Operation details from our test
    const operationName = 'projects/gensy-463405/locations/us-central1/publishers/google/models/veo-2.0-generate-001/operations/82184331-1096-4f1a-9e91-8b3152a77c09'
    const jobId = '82184331-1096-4f1a-9e91-8b3152a77c09'
    
    console.log(`🎯 Checking operation: ${jobId}`)
    
    // First, let's try to list all operations to see what's available
    console.log('📋 Listing all operations...')

    const listResponse = await fetch(
      'https://us-central1-aiplatform.googleapis.com/v1/projects/gensy-463405/locations/us-central1/operations',
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken.token}`,
          'Content-Type': 'application/json'
        }
      }
    )

    console.log(`📡 List Operations Response Status: ${listResponse.status}`)

    if (listResponse.ok) {
      const listData = await listResponse.json()
      console.log('📋 Available Operations:')

      if (listData.operations && listData.operations.length > 0) {
        listData.operations.forEach((op, index) => {
          console.log(`${index + 1}. ${op.name}`)
          console.log(`   Done: ${op.done}`)
          console.log(`   Metadata: ${JSON.stringify(op.metadata || {}, null, 2)}`)
          console.log('---')
        })

        // Look for our specific operation
        const ourOperation = listData.operations.find(op =>
          op.name.includes(jobId) || op.name.includes('82184331-1096-4f1a-9e91-8b3152a77c09')
        )

        if (ourOperation) {
          console.log('🎯 Found our operation!')
          console.log('📋 Operation Data:', JSON.stringify(ourOperation, null, 2))

          // Analyze the operation status
          if (ourOperation.done) {
            if (ourOperation.response) {
              console.log('🎉 VIDEO GENERATION COMPLETED!')
              console.log('📹 Video data available in response')
            } else if (ourOperation.error) {
              console.log('❌ VIDEO GENERATION FAILED!')
              console.log('🚨 Error:', ourOperation.error)
            }
          } else {
            console.log('⏳ VIDEO GENERATION IN PROGRESS...')
          }

          return
        } else {
          console.log('❌ Our specific operation not found in the list')
        }
      } else {
        console.log('📋 No operations found')
      }
    } else {
      const errorText = await listResponse.text()
      console.error(`❌ List Operations Error: ${listResponse.status} - ${errorText}`)
    }

    // If we can't find it in the list, the operation might have completed and been cleaned up
    console.log('💡 The operation might have already completed and been cleaned up by Google')
    console.log('💡 This is normal behavior - Google removes completed operations after some time')

    
  } catch (error) {
    console.error('❌ Error checking operation status:', error)
  }
}

// Run the check
checkOperationStatus()
