{"designSystemProfile": {"name": "Krea AI Creative Suite", "version": "1.0.0", "description": "A design system for a modern, clean, and intuitive AI-powered creative web application. It features dual themes (dark and light) and focuses on clarity, spacing, and component-based design.", "global": {"themes": [{"name": "dark", "description": "Used for the main dashboard and landing pages to create a sophisticated, focused environment.", "palette": {"background": {"primary": "#121212", "secondary": "#1C1C1E"}, "text": {"primary": "#FFFFFF", "secondary": "#B0B0B0", "inverse": "#000000"}, "accent": {"primary": "#3B82F6", "secondary": "#10B981"}, "borders": "#333333", "info": "#F59E0B"}}, {"name": "light", "description": "Used for tool-specific interfaces (Image, Video, Enhance), galleries, and modals to provide a bright, clean workspace.", "palette": {"background": {"primary": "#FFFFFF", "secondary": "#F3F4F6"}, "text": {"primary": "#111827", "secondary": "#6B7280", "inverse": "#FFFFFF"}, "accent": {"primary": "#3B82F6", "secondary": "#10B981"}, "borders": "#E5E7EB", "info": "#F59E0B"}}], "typography": {"fontFamily": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif", "styles": [{"role": "display", "fontWeight": 700, "fontSize": "48px"}, {"role": "heading1", "fontWeight": 700, "fontSize": "32px"}, {"role": "heading2", "fontWeight": 600, "fontSize": "24px"}, {"role": "body", "fontWeight": 400, "fontSize": "16px"}, {"role": "label", "fontWeight": 500, "fontSize": "14px"}, {"role": "caption", "fontWeight": 400, "fontSize": "12px"}]}, "spacing": {"baseUnit": "4px", "scale": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "xxl": "48px"}}, "borderRadius": {"small": "4px", "medium": "8px", "large": "12px", "full": "9999px"}, "shadows": {"subtle": "0 1px 2px 0 rgb(0 0 0 / 0.05)", "medium": "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)", "large": "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)"}}, "layout": {"header": {"description": "A persistent global header.", "structure": "Contains a logo on the left, a center-aligned icon-based navigation group, and user actions (credits, upgrade button, profile avatar) on the right.", "height": "64px", "padding": "0 32px"}, "toolLayout": {"description": "Standard layout for a focused creation tool like Image or Video.", "structure": "Consists of the global header, a narrow left sidebar for asset history, and a main content area with a single, centered primary component."}, "dashboardLayout": {"description": "Layout for the main homepage.", "structure": "Consists of the global header followed by horizontally stacked sections for 'Featured', 'Generate', and 'Gallery'."}}, "components": {"button": {"description": "General purpose button.", "baseStyle": {"borderRadius": "full", "padding": "12px 24px", "fontWeight": 600, "transition": "all 0.2s ease-in-out"}, "variants": {"primary": {"backgroundColor": "accent.primary", "color": "text.inverse"}, "secondary": {"backgroundColor": "background.secondary", "color": "text.primary", "border": "1px solid", "borderColor": "borders"}, "tertiary": {"backgroundColor": "transparent", "color": "text.primary"}}, "states": {"hover": {"opacity": 0.85}, "disabled": {"opacity": 0.5, "cursor": "not-allowed"}}}, "card": {"description": "A container for grouped content.", "baseStyle": {"borderRadius": "large", "padding": "24px", "backgroundColor": "background.secondary", "boxShadow": "subtle"}, "variants": {"toolCard": {"display": "flex", "alignItems": "center", "gap": "16px"}, "pricingCard": {"display": "flex", "flexDirection": "column", "gap": "16px", "border": "2px solid", "borderColor": "accent.primary"}, "imageCard": {"padding": "0", "overflow": "hidden"}}}, "modal": {"description": "A dialog window that overlays the main content.", "structure": {"overlay": {"position": "fixed", "inset": 0, "backgroundColor": "rgba(0, 0, 0, 0.5)", "backdropFilter": "blur(4px)"}, "container": {"position": "fixed", "top": "50%", "left": "50%", "transform": "translate(-50%, -50%)", "backgroundColor": "background.primary", "borderRadius": "large", "padding": "32px", "boxShadow": "large", "minWidth": "400px"}}}, "inputField": {"description": "Text input field.", "baseStyle": {"backgroundColor": "background.secondary", "color": "text.primary", "borderRadius": "full", "padding": "12px 20px", "border": "1px solid", "borderColor": "borders"}, "states": {"focus": {"borderColor": "accent.primary", "boxShadow": "0 0 0 2px rgba(59, 130, 246, 0.2)"}}}, "promptComponent": {"description": "The central input component for generation tools.", "structure": "A horizontally grouped input field and a primary 'Generate' button, with a sub-bar below for options like 'Style', 'Aspect Ratio', etc."}, "popover": {"description": "A transient view that appears above other content.", "baseStyle": {"backgroundColor": "background.primary", "borderRadius": "medium", "boxShadow": "medium", "padding": "8px"}, "placement": "Anchored to a trigger element."}, "tooltip": {"description": "A small, contextual helper text bubble.", "baseStyle": {"backgroundColor": "#111827", "color": "#FFFFFF", "borderRadius": "full", "padding": "4px 12px", "fontSize": "12px", "fontWeight": 500}}, "masonryGrid": {"description": "Pinterest-style grid for displaying images of variable sizes.", "structure": "A column-based layout where items are added to the shortest column to create a balanced, gap-less appearance."}}}}